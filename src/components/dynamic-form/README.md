# Dynamic Form

A comprehensive, configurable React component for creating both single-step and multi-step forms based on JSON configuration. Built with react-hook-form, Zod validation, and shadcn/ui components.

## Features

- 🎯 **JSON-Driven Configuration** - Define entire forms using JSON
- 🔧 **Dynamic Validation** - Zod schemas generated from configuration
- 🎨 **Conditional Fields** - Show/hide fields based on other field values
- 📱 **Responsive Design** - Mobile-friendly with adaptive layouts
- 🌐 **Internationalization** - Full i18n support with next-intl
- ♿ **Accessibility** - WCAG compliant with keyboard navigation
- 🎭 **Customizable UI** - Built on shadcn/ui components
- 📊 **Progress Tracking** - Visual step indicators and progress bars
- 🔄 **Dynamic Arrays** - Handle complex arrays with JSON-driven field schemas
- 🧩 **Pure JSON Configuration** - No hardcoded React components required

## Table of Contents

1. [Quick Start](#quick-start)
2. [Configuration Reference](#configuration-reference)
3. [Field Types Reference](#field-types-reference)
4. [Validation Rules](#validation-rules)
5. [Conditional Logic](#conditional-logic)
6. [Grid Layouts](#grid-layouts)
7. [Internationalization](#internationalization)
8. [Testing](#testing)
9. [Complete Examples](#complete-examples)

## Supported Field Types

- **Input** - Text, email, password, number inputs with validation
- **Textarea** - Multi-line text with character counter and length limits
- **Select** - Single selection dropdown with search functionality
- **Multi-Select** - Multiple selection dropdown with customizable display
- **Checkbox** - Boolean input with custom styling and validation
- **Radio** - Single selection from predefined options
- **Switch** - Toggle input for boolean values
- **Date/DateTime/Month** - Date picker with various modes and time support
- **Phone** - International phone number input with country codes
- **Country** - Country/nationality selection with search
- **Upload** - File upload with type, size, and count validation
- **Dynamic Array** - Complex arrays with embedded React components and add/remove functionality

## Quick Start

### Basic Usage

```tsx
import { DynamicForm, FormConfig } from '@/components/dynamic-form'

const config: FormConfig = {
  title: 'User Registration',
  steps: [
    {
      id: 'personal',
      title: 'Personal Information',
      fields: [
        {
          name: 'firstName',
          type: 'input',
          label: 'First Name',
          isRequired: true,
          validation: [
            { type: 'required', message: 'First name is required' },
            { type: 'minLength', value: 2, message: 'Minimum 2 characters' },
          ],
        },
        {
          name: 'email',
          type: 'input',
          label: 'Email',
          isRequired: true,
          validation: [
            { type: 'required', message: 'Email is required' },
            { type: 'email', message: 'Invalid email format' },
          ],
        },
      ],
    },
  ],
}

const callbacks = {
  onSubmit: async (data) => {
    console.log('Form submitted:', data)
    // Handle form submission
  },
}

function RegistrationForm() {
  return <DynamicForm config={config} callbacks={callbacks} />
}
```

### Advanced Configuration with Conditional Fields

```tsx
const advancedConfig: FormStepperConfig = {
  title: 'Advanced Form',
  description: 'Demonstrating conditional fields and grid layout',
  showStepNumbers: true,
  showProgressBar: true,
  allowStepNavigation: false,
  validateOnStepChange: true,
  steps: [
    {
      id: 'step1',
      title: 'Contact Info',
      columns: 2, // Grid layout
      fields: [
        {
          name: 'phone',
          type: 'phone',
          label: 'Phone Number',
          colSpan: 1,
          isRequired: true,
        },
        {
          name: 'country',
          type: 'country',
          label: 'Country',
          colSpan: 1,
          isRequired: true,
        },
        {
          name: 'hasExperience',
          type: 'checkbox',
          label: 'I have experience',
          colSpan: 2,
        },
        {
          name: 'experienceYears',
          type: 'input',
          label: 'Years of Experience',
          colSpan: 2,
          showIf: {
            field: 'hasExperience',
            operator: 'equals',
            value: true,
          },
          validation: [{ type: 'required', message: 'Required when experience is selected' }],
        },
      ],
    },
  ],
}
```

## Configuration Reference

### FormStepperConfig

| Property               | Type         | Default    | Description                    |
| ---------------------- | ------------ | ---------- | ------------------------------ |
| `title`                | string       | -          | Form title                     |
| `description`          | string       | -          | Form description               |
| `logoUrl`              | string       | -          | Logo image URL                 |
| `steps`                | StepConfig[] | -          | Array of form steps            |
| `submitButtonText`     | string       | "Submit"   | Submit button text             |
| `nextButtonText`       | string       | "Next"     | Next button text               |
| `previousButtonText`   | string       | "Previous" | Previous button text           |
| `showStepNumbers`      | boolean      | true       | Show step numbers              |
| `showProgressBar`      | boolean      | true       | Show progress bar              |
| `allowStepNavigation`  | boolean      | false      | Allow clicking step indicators |
| `validateOnStepChange` | boolean      | true       | Validate before step change    |

### StepConfig

| Property      | Type          | Default | Description            |
| ------------- | ------------- | ------- | ---------------------- |
| `id`          | string        | -       | Unique step identifier |
| `title`       | string        | -       | Step title             |
| `description` | string        | -       | Step description       |
| `columns`     | number        | 1       | Grid columns (1-12)    |
| `fields`      | FieldConfig[] | -       | Array of form fields   |

### FieldConfig

| Property       | Type             | Default | Description              |
| -------------- | ---------------- | ------- | ------------------------ |
| `name`         | string           | -       | Field name (unique)      |
| `type`         | FieldType        | -       | Field type               |
| `label`        | string           | -       | Field label              |
| `placeholder`  | string           | -       | Placeholder text         |
| `helperText`   | string           | -       | Helper text              |
| `tooltip`      | string           | -       | Tooltip text             |
| `isRequired`   | boolean          | false   | Required field           |
| `disabled`     | boolean          | false   | Disabled field           |
| `colSpan`      | number           | 1       | Grid column span (1-12)  |
| `defaultValue` | any              | -       | Default field value      |
| `validation`   | ValidationRule[] | -       | Validation rules         |
| `showIf`       | Condition        | -       | Conditional display      |
| `options`      | Option[]         | -       | Options for select/radio |

## Validation Rules

### Available Rule Types

| Rule Type   | Description                 | Example Value | Use Cases                    |
| ----------- | --------------------------- | ------------- | ---------------------------- |
| `required`  | Field must have a value     | N/A           | All required fields          |
| `minLength` | Minimum string/array length | `2`           | Names, passwords, selections |
| `maxLength` | Maximum string/array length | `100`         | Descriptions, skill lists    |
| `min`       | Minimum numeric value       | `18`          | Age, quantity limits         |
| `max`       | Maximum numeric value       | `65`          | Age, quantity limits         |
| `email`     | Valid email format          | N/A           | Email addresses              |
| `regex`     | Custom pattern matching     | `"^[0-9]+$"`  | Phone numbers, IDs           |
| `isTrue`    | Must be true (checkboxes)   | N/A           | Terms agreement              |

### Common Validation Examples

**Email Validation:**

```json
{
  "validation": [
    { "type": "required", "message": "Email is required" },
    { "type": "email", "message": "Please enter a valid email address" }
  ]
}
```

**Password Validation:**

```json
{
  "validation": [
    { "type": "required", "message": "Password is required" },
    { "type": "minLength", "value": 8, "message": "Password must be at least 8 characters" },
    {
      "type": "regex",
      "pattern": "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)",
      "message": "Password must contain uppercase, lowercase, and number"
    }
  ]
}
```

**Numeric Range Validation:**

```json
{
  "validation": [
    { "type": "required", "message": "Age is required" },
    { "type": "min", "value": 18, "message": "Must be at least 18 years old" },
    { "type": "max", "value": 120, "message": "Please enter a valid age" }
  ]
}
```

## Conditional Logic

Conditional fields allow you to show or hide form fields based on the values of other fields. This creates dynamic, responsive forms that adapt to user input.

### Simple Conditions

**Show field when another field equals a value:**

```json
{
  "showIf": {
    "field": "hasExperience",
    "operator": "equals",
    "value": true
  }
}
```

**Show field when another field is not empty:**

```json
{
  "showIf": {
    "field": "otherField",
    "operator": "isNotEmpty"
  }
}
```

### Complex Conditions

**Multiple conditions with AND logic:**

```json
{
  "showIf": {
    "operator": "and",
    "conditions": [
      { "field": "age", "operator": "greaterThan", "value": 18 },
      { "field": "country", "operator": "equals", "value": "US" }
    ]
  }
}
```

**Multiple conditions with OR logic:**

```json
{
  "showIf": {
    "operator": "or",
    "conditions": [
      { "field": "contactMethod", "operator": "equals", "value": "phone" },
      { "field": "contactMethod", "operator": "equals", "value": "sms" }
    ]
  }
}
```

### Available Condition Operators

| Operator             | Description                  | Example Use Case                         |
| -------------------- | ---------------------------- | ---------------------------------------- |
| `equals`             | Exact match                  | Show field when dropdown = "option"      |
| `notEquals`          | Not equal to                 | Hide field when status ≠ "active"        |
| `contains`           | Array/string contains value  | Show when skills contain "javascript"    |
| `notContains`        | Array/string doesn't contain | Hide when interests don't contain "tech" |
| `greaterThan`        | Numeric comparison           | Show when age > 18                       |
| `lessThan`           | Numeric comparison           | Show when experience < 5                 |
| `greaterThanOrEqual` | Numeric comparison           | Show when score ≥ 80                     |
| `lessThanOrEqual`    | Numeric comparison           | Show when hours ≤ 40                     |
| `isEmpty`            | Field is empty/null          | Show when optional field is empty        |
| `isNotEmpty`         | Field has value              | Show when required field is filled       |

## Grid Layouts

Configure responsive grid layouts to organize fields in columns and control their spacing.

### Basic Grid Configuration

Set the number of columns at the step level and control field spanning:

```json
{
  "columns": 2,
  "fields": [
    { "name": "firstName", "colSpan": 1 },
    { "name": "lastName", "colSpan": 1 },
    { "name": "email", "colSpan": 2 }
  ]
}
```

### Responsive Grid Examples

**Two-column layout:**

```json
{
  "columns": 2,
  "fields": [
    { "name": "firstName", "colSpan": 1 },
    { "name": "lastName", "colSpan": 1 },
    { "name": "email", "colSpan": 2 }
  ]
}
```

**Three-column layout:**

```json
{
  "columns": 3,
  "fields": [
    { "name": "day", "colSpan": 1 },
    { "name": "month", "colSpan": 1 },
    { "name": "year", "colSpan": 1 },
    { "name": "fullAddress", "colSpan": 3 }
  ]
}
```

## Internationalization

All text can be internationalized using translation keys with next-intl:

```json
{
  "title": "FORM.REGISTRATION.TITLE",
  "fields": [
    {
      "label": "FORM.FIELDS.FIRST_NAME.LABEL",
      "placeholder": "FORM.FIELDS.FIRST_NAME.PLACEHOLDER",
      "validation": [
        {
          "type": "required",
          "message": "FORM.FIELDS.FIRST_NAME.REQUIRED"
        }
      ]
    }
  ]
}
```

## Testing

### Suggested Test Cases

1. **Form Validation**

   - Required field validation
   - Email format validation
   - Custom regex patterns
   - Cross-field validation

2. **Step Navigation**

   - Forward/backward navigation
   - Step validation on change
   - Progress tracking
   - Step indicator clicks

3. **Conditional Fields**

   - Simple condition evaluation
   - Complex boolean logic
   - Field visibility updates
   - Dependency validation

4. **Component Integration**
   - All field types render correctly
   - Form submission handling
   - Error state display
   - Loading states

### Example Test

```tsx
import { fireEvent, render, screen, waitFor } from '@testing-library/react'

import { DynamicForm } from '@/components/dynamic-form'

test('validates required fields', async () => {
  const config = {
    steps: [
      {
        id: 'test',
        title: 'Test',
        fields: [
          {
            name: 'email',
            type: 'input',
            label: 'Email',
            isRequired: true,
            validation: [{ type: 'required', message: 'Email is required' }],
          },
        ],
      },
    ],
  }

  const onSubmit = jest.fn()

  render(<DynamicForm config={config} callbacks={{ onSubmit }} />)

  fireEvent.click(screen.getByText('Submit'))

  await waitFor(() => {
    expect(screen.getByText('Email is required')).toBeInTheDocument()
  })
})
```

## Field Types Reference

This section provides complete configuration examples for each supported field type. Copy and paste these examples into your form configuration and customize as needed.

### Input Field

Basic text input with validation support and optional suffix display.

```json
{
  "name": "firstName",
  "type": "input",
  "label": "First Name",
  "placeholder": "Enter your first name",
  "isRequired": true,
  "colSpan": 1,
  "suffix": "€",
  "validation": [
    { "type": "required", "message": "First name is required" },
    { "type": "minLength", "value": 2, "message": "Minimum 2 characters required" },
    { "type": "maxLength", "value": 50, "message": "Maximum 50 characters allowed" }
  ]
}
```

**Special Properties:** `suffix` - Text/symbol to display after input (e.g., currency symbols)

### Textarea Field

Multi-line text input with character counting and length limits.

```json
{
  "name": "description",
  "type": "textarea",
  "label": "Description",
  "placeholder": "Enter a detailed description...",
  "isRequired": false,
  "colSpan": 2,
  "maxLength": 500,
  "showCounter": true,
  "validation": [{ "type": "maxLength", "value": 500, "message": "Description must be 500 characters or less" }]
}
```

**Special Properties:** `maxLength` - Maximum character limit, `showCounter` - Display character counter

### Select Field

Single selection dropdown with search functionality.

```json
{
  "name": "category",
  "type": "select",
  "label": "Category",
  "placeholder": "Choose a category",
  "isRequired": true,
  "colSpan": 1,
  "autoComplete": true,
  "searchPlaceholder": "Search categories...",
  "emptyMessage": "No categories found",
  "options": [
    { "value": "tech", "label": "Technology" },
    { "value": "business", "label": "Business" },
    { "value": "design", "label": "Design" }
  ],
  "validation": [{ "type": "required", "message": "Please select a category" }]
}
```

**Special Properties:** `autoComplete`, `searchPlaceholder`, `emptyMessage`, `options`

### Multi-Select Field

Multiple selection dropdown with customizable display.

```json
{
  "name": "skills",
  "type": "multiselect",
  "label": "Skills",
  "placeholder": "Select your skills",
  "isRequired": false,
  "colSpan": 2,
  "maxCount": 3,
  "options": [
    { "value": "javascript", "label": "JavaScript" },
    { "value": "react", "label": "React" },
    { "value": "nodejs", "label": "Node.js" },
    { "value": "python", "label": "Python" }
  ],
  "validation": [
    { "type": "minLength", "value": 1, "message": "Please select at least one skill" },
    { "type": "maxLength", "value": 5, "message": "Please select no more than 5 skills" }
  ]
}
```

**Special Properties:** `maxCount` - Maximum items to display before showing "+X more", `options`

### Checkbox Field

Boolean input for agreements, preferences, etc.

```json
{
  "name": "newsletter",
  "type": "checkbox",
  "label": "Subscribe to our newsletter for updates",
  "isRequired": false,
  "colSpan": 1,
  "defaultValue": false
}
```

**Required Checkbox (Terms Agreement):**

```json
{
  "name": "terms",
  "type": "checkbox",
  "label": "I agree to the Terms of Service",
  "isRequired": true,
  "colSpan": 1,
  "validation": [{ "type": "isTrue", "message": "You must agree to the Terms of Service" }]
}
```

### Phone Field

International phone number input with automatic country code detection and validation.

```json
{
  "name": "phoneNumber",
  "type": "phone",
  "label": "Phone Number",
  "placeholder": "Enter your phone number",
  "isRequired": true,
  "colSpan": 1,
  "validation": [{ "type": "required", "message": "Phone number is required" }]
}
```

**Features:** Automatic country code detection, international format validation, country flag display

### Country Field

Country/nationality selection with search functionality.

```json
{
  "name": "country",
  "type": "country",
  "label": "Country",
  "placeholder": "Select your country",
  "isRequired": true,
  "colSpan": 1,
  "isNationality": false,
  "autoComplete": true,
  "searchPlaceholder": "Search countries...",
  "emptyMessage": "No countries found",
  "validation": [{ "type": "required", "message": "Please select your country" }]
}
```

**Special Properties:** `isNationality` - Whether to show as nationality vs country

### Date/DateTime Fields

Date picker with various modes and time support.

**Date Only:**

```json
{
  "name": "birthDate",
  "type": "date",
  "label": "Date of Birth",
  "isRequired": false,
  "colSpan": 1,
  "mode": "date"
}
```

**Date and Time:**

```json
{
  "name": "appointmentDateTime",
  "type": "datetime",
  "label": "Appointment Date & Time",
  "isRequired": true,
  "colSpan": 1,
  "includeTime": true,
  "includeSeconds": false,
  "validation": [{ "type": "required", "message": "Please select an appointment time" }]
}
```

**Month Only:**

```json
{
  "name": "startMonth",
  "type": "month",
  "label": "Start Month",
  "isRequired": false,
  "colSpan": 1,
  "mode": "month"
}
```

**Special Properties:** `mode`, `includeTime`, `includeSeconds`

### Upload Field

File upload with comprehensive validation and support for single or multiple files.

**Single File Upload:**

```json
{
  "name": "profilePicture",
  "type": "upload",
  "label": "Profile Picture",
  "isRequired": false,
  "colSpan": 1,
  "accept": "image/*",
  "multiple": false,
  "maxFiles": 1,
  "maxSize": 5,
  "helperText": "Upload a profile picture (JPG, PNG, max 5MB)"
}
```

**Multiple File Upload:**

```json
{
  "name": "documents",
  "type": "upload",
  "label": "Supporting Documents",
  "isRequired": false,
  "colSpan": 1,
  "accept": ".pdf,.doc,.docx,.txt",
  "multiple": true,
  "maxFiles": 5,
  "maxSize": 10,
  "helperText": "Upload documents (PDF, DOC, DOCX, TXT, max 10MB each, up to 5 files)"
}
```

**Special Properties:** `accept`, `multiple`, `maxFiles`, `maxSize`, `helperText`

## Complete Examples

### Job Application Form

Here's a complete, real-world form configuration that demonstrates all features:

```json
{
  "title": "Job Application Form",
  "description": "Apply for a position at our company",
  "submitButtonText": "Submit Application",
  "nextButtonText": "Continue",
  "previousButtonText": "Back",
  "showStepNumbers": true,
  "showProgressBar": true,
  "allowStepNavigation": false,
  "validateOnStepChange": true,
  "steps": [
    {
      "id": "personal",
      "title": "Personal Information",
      "description": "Tell us about yourself",
      "columns": 2,
      "fields": [
        {
          "name": "firstName",
          "type": "input",
          "label": "First Name",
          "placeholder": "Enter your first name",
          "isRequired": true,
          "colSpan": 1,
          "validation": [
            { "type": "required", "message": "First name is required" },
            { "type": "minLength", "value": 2, "message": "Minimum 2 characters" }
          ]
        },
        {
          "name": "lastName",
          "type": "input",
          "label": "Last Name",
          "placeholder": "Enter your last name",
          "isRequired": true,
          "colSpan": 1,
          "validation": [{ "type": "required", "message": "Last name is required" }]
        },
        {
          "name": "email",
          "type": "input",
          "label": "Email Address",
          "placeholder": "<EMAIL>",
          "isRequired": true,
          "colSpan": 2,
          "validation": [
            { "type": "required", "message": "Email is required" },
            { "type": "email", "message": "Invalid email format" }
          ]
        },
        {
          "name": "phone",
          "type": "phone",
          "label": "Phone Number",
          "isRequired": true,
          "colSpan": 1
        },
        {
          "name": "country",
          "type": "country",
          "label": "Country",
          "isRequired": true,
          "colSpan": 1
        }
      ]
    },
    {
      "id": "experience",
      "title": "Experience",
      "description": "Tell us about your background",
      "columns": 1,
      "fields": [
        {
          "name": "hasExperience",
          "type": "checkbox",
          "label": "I have relevant work experience",
          "isRequired": false,
          "colSpan": 1
        },
        {
          "name": "yearsExperience",
          "type": "input",
          "label": "Years of Experience",
          "placeholder": "Enter number of years",
          "isRequired": true,
          "colSpan": 1,
          "showIf": {
            "field": "hasExperience",
            "operator": "equals",
            "value": true
          },
          "validation": [
            { "type": "required", "message": "Please enter years of experience" },
            { "type": "regex", "pattern": "^[0-9]+$", "message": "Please enter a valid number" }
          ]
        },
        {
          "name": "skills",
          "type": "multiselect",
          "label": "Technical Skills",
          "placeholder": "Select your skills",
          "isRequired": true,
          "colSpan": 1,
          "maxCount": 3,
          "options": [
            { "value": "javascript", "label": "JavaScript" },
            { "value": "react", "label": "React" },
            { "value": "nodejs", "label": "Node.js" },
            { "value": "python", "label": "Python" }
          ],
          "validation": [{ "type": "minLength", "value": 1, "message": "Please select at least one skill" }]
        },
        {
          "name": "resume",
          "type": "upload",
          "label": "Resume/CV",
          "isRequired": true,
          "colSpan": 1,
          "accept": ".pdf,.doc,.docx",
          "multiple": false,
          "maxFiles": 1,
          "maxSize": 5,
          "helperText": "Upload your resume (PDF, DOC, DOCX, max 5MB)",
          "validation": [{ "type": "required", "message": "Please upload your resume" }]
        }
      ]
    }
  ]
}
```

## Specialized Step Components

The dynamic form system now supports specialized step components for complex use cases that go beyond standard form fields. These specialized steps are configured through the step type system and provide dedicated components for specific business logic.

### Supported Specialized Steps

- **Contracts Step**: Manages insurance contracts with specialized UI and validation
- **Payment Step**: Handles payment methods with different forms and validation logic

### Configuration

Specialized steps are configured using the discriminated step type system:

### Example: Contracts Step

```json
{
  "id": "contracts",
  "type": "contracts",
  "title": "Insurance Contracts",
  "description": "Manage your insurance contracts",
  "config": {
    "allowMultipleContracts": true,
    "maxContracts": 5,
    "showMiniCards": true
  }
}
```

### Example: Payment Step

```json
{
  "id": "payment",
  "type": "payment",
  "title": "Payment Methods",
  "description": "Configure your payment preferences",
  "config": {
    "allowedPaymentTypes": ["creditCard", "sepa"],
    "requireBillingAddress": true,
    "maxPaymentMethods": 3
  }
}
```

### Step Dependencies

Specialized steps require external dependencies that are provided through the `stepDependencies` prop:

```typescript
const stepDependencies = {
  contracts: {
    providers: contractProviders,
    products: contractProducts,
    categoryOptions: contractCategories,
  },
  payment: {
    userProfile: currentUser,
  },
}
```

### Usage with DynamicForm

To use specialized steps, provide the step dependencies when rendering the DynamicForm:

```typescript
import { DynamicForm } from '@/components/dynamic-form'

function MyForm() {
  const stepDependencies = {
    contracts: {
      providers: contractProviders,
      products: contractProducts,
      categoryOptions: contractCategories
    },
    payment: {
      userProfile: currentUser
    }
  }

  return (
    <DynamicForm
      config={formConfig}
      callbacks={callbacks}
      stepDependencies={stepDependencies}
    />
  )
}
```

### Complete Example

Here's a complete example showing how to use specialized steps in a multi-step form:

```json
{
  "title": "Insurance Application with Specialized Steps",
  "steps": [
    {
      "id": "personal",
      "type": "form",
      "title": "Personal Information",
      "fields": [
        {
          "name": "firstName",
          "type": "input",
          "label": "First Name",
          "isRequired": true
        },
        {
          "name": "email",
          "type": "input",
          "label": "Email",
          "isRequired": true,
          "validation": [{ "type": "email", "message": "Invalid email" }]
        }
      ]
    },
    {
      "id": "contracts",
      "type": "contracts",
      "title": "Insurance Contracts",
      "description": "Manage your insurance contracts",
      "config": {
        "allowMultipleContracts": true,
        "maxContracts": 5,
        "showMiniCards": true
      }
    },
    {
      "id": "payment",
      "type": "payment",
      "title": "Payment Methods",
      "description": "Configure your payment preferences",
      "config": {
        "allowedPaymentTypes": ["creditCard", "sepa"],
        "requireBillingAddress": true,
        "maxPaymentMethods": 3
      }
    }
  ]
}
```

### Architecture Benefits

The new specialized step system provides several advantages:

- **Type Safety**: Discriminated union types ensure compile-time safety
- **Separation of Concerns**: Complex business logic is handled by specialized components
- **Reusability**: Existing specialized components can be easily integrated
- **Extensibility**: Easy to add new specialized step types
- **Maintainability**: Clear separation between form fields and complex components

### Migration from Dynamic Arrays

If you were previously using the `dynamic-array` field type, you can migrate to specialized steps:

1. **Identify Complex Arrays**: Arrays that require specialized UI or business logic
2. **Create Specialized Steps**: Convert complex arrays to dedicated step components
3. **Update Configuration**: Use the new step type system instead of field types
4. **Provide Dependencies**: Pass required data through `stepDependencies`

### Best Practices

1. **Use Form Steps for Simple Fields**: Standard form fields should use `type: "form"`
2. **Use Specialized Steps for Complex Logic**: Business-specific components should use specialized types
3. **Provide Required Dependencies**: Ensure all external data is provided through `stepDependencies`
4. **Maintain Type Safety**: Use TypeScript interfaces for step configurations
5. **Test Step Integration**: Verify that specialized steps integrate properly with form navigation
