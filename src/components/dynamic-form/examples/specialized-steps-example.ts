/**
 * Example configuration showing how to use the new specialized step system
 * 
 * This example demonstrates:
 * 1. Form steps with standard fields
 * 2. Contracts step with specialized configuration
 * 3. Payment step with specialized configuration
 * 4. How to provide step dependencies
 */

import type { FormConfig, StepDependencies } from '../types'

// Example form configuration with specialized steps
export const exampleFormConfig: FormConfig = {
  title: "Insurance Application with Specialized Steps",
  description: "Complete your insurance application using our step-by-step process",
  steps: [
    // Standard form step
    {
      id: "personal",
      type: "form",
      title: "Personal Information",
      description: "Please provide your basic personal information",
      columns: 2,
      fields: [
        {
          name: "firstName",
          type: "input",
          label: "First Name",
          isRequired: true,
          colSpan: 1,
          validation: [
            { type: "required", message: "First name is required" }
          ]
        },
        {
          name: "lastName",
          type: "input",
          label: "Last Name",
          isRequired: true,
          colSpan: 1,
          validation: [
            { type: "required", message: "Last name is required" }
          ]
        },
        {
          name: "email",
          type: "input",
          label: "Email Address",
          isRequired: true,
          colSpan: 2,
          validation: [
            { type: "required", message: "Email is required" },
            { type: "email", message: "Please enter a valid email address" }
          ]
        },
        {
          name: "phone",
          type: "phone",
          label: "Phone Number",
          isRequired: true,
          colSpan: 1
        },
        {
          name: "dateOfBirth",
          type: "date",
          label: "Date of Birth",
          isRequired: true,
          colSpan: 1
        }
      ]
    },
    
    // Specialized contracts step
    {
      id: "contracts",
      type: "contracts",
      title: "Insurance Contracts",
      description: "Manage your insurance contracts and coverage options",
      config: {
        allowMultipleContracts: true,
        maxContracts: 5,
        showMiniCards: true,
        requiredFields: ["contractType", "coverage", "premium"]
      }
    },
    
    // Specialized payment step
    {
      id: "payment",
      type: "payment",
      title: "Payment Methods",
      description: "Configure your payment preferences and billing information",
      config: {
        allowedPaymentTypes: ["creditCard", "sepa"],
        requireBillingAddress: true,
        maxPaymentMethods: 3
      }
    },
    
    // Another standard form step
    {
      id: "preferences",
      type: "form",
      title: "Communication Preferences",
      description: "How would you like us to communicate with you?",
      columns: 1,
      fields: [
        {
          name: "preferredContactMethod",
          type: "radio",
          label: "Preferred Contact Method",
          isRequired: true,
          options: [
            { value: "email", label: "Email" },
            { value: "phone", label: "Phone" },
            { value: "sms", label: "SMS" }
          ]
        },
        {
          name: "marketingConsent",
          type: "checkbox",
          label: "I agree to receive marketing communications",
          isRequired: false
        },
        {
          name: "newsletter",
          type: "switch",
          label: "Subscribe to Newsletter",
          isRequired: false
        }
      ]
    }
  ]
}

// Example step dependencies
export const exampleStepDependencies: StepDependencies = {
  contracts: {
    providers: [
      { id: 1, name: "Insurance Provider A", code: "IPA" },
      { id: 2, name: "Insurance Provider B", code: "IPB" },
      { id: 3, name: "Insurance Provider C", code: "IPC" }
    ],
    products: [
      { id: 1, name: "Health Insurance", category: "health", providerId: 1 },
      { id: 2, name: "Car Insurance", category: "auto", providerId: 1 },
      { id: 3, name: "Home Insurance", category: "property", providerId: 2 },
      { id: 4, name: "Life Insurance", category: "life", providerId: 3 }
    ],
    categoryOptions: [
      { value: "health", label: "Health Insurance" },
      { value: "auto", label: "Auto Insurance" },
      { value: "property", label: "Property Insurance" },
      { value: "life", label: "Life Insurance" }
    ]
  },
  payment: {
    userProfile: {
      id: "user-123",
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      address: {
        street: "123 Main St",
        city: "Anytown",
        zipCode: "12345",
        country: "US"
      }
    }
  }
}

// Example usage in a React component:
/*
import { DynamicForm } from '@/components/dynamic-form'
import { exampleFormConfig, exampleStepDependencies } from './specialized-steps-example'

function InsuranceApplicationForm() {
  const handleSubmit = async (data: any) => {
    console.log('Form submitted:', data)
    // Handle form submission
  }

  const callbacks = {
    onSubmit: handleSubmit,
    onStepChange: ({ currentStepData, currentStepId, step, direction }) => {
      console.log('Step changed:', { currentStepData, currentStepId, step, direction })
    }
  }

  return (
    <DynamicForm
      config={exampleFormConfig}
      callbacks={callbacks}
      stepDependencies={exampleStepDependencies}
    />
  )
}
*/

// Type-safe configuration validation
export function validateSpecializedStepConfig(config: FormConfig): boolean {
  for (const step of config.steps) {
    switch (step.type) {
      case 'form':
        if (!step.fields || step.fields.length === 0) {
          console.error(`Form step "${step.id}" must have at least one field`)
          return false
        }
        break
      case 'contracts':
        if (step.config?.maxContracts && step.config.maxContracts < 1) {
          console.error(`Contracts step "${step.id}" maxContracts must be at least 1`)
          return false
        }
        break
      case 'payment':
        if (step.config?.maxPaymentMethods && step.config.maxPaymentMethods < 1) {
          console.error(`Payment step "${step.id}" maxPaymentMethods must be at least 1`)
          return false
        }
        break
      default:
        console.error(`Unknown step type: ${(step as any).type}`)
        return false
    }
  }
  return true
}

// Migration helper for converting old dynamic-array configurations
export function migrateDynamicArrayToSpecializedStep(
  oldFieldConfig: any
): { stepConfig: any; dependencies: any } | null {
  if (oldFieldConfig.type !== 'dynamic-array') {
    return null
  }

  const componentName = oldFieldConfig.componentName || oldFieldConfig.modal?.component

  switch (componentName) {
    case 'contracts':
    case 'contracts-modal':
      return {
        stepConfig: {
          id: oldFieldConfig.name,
          type: 'contracts',
          title: oldFieldConfig.label,
          description: oldFieldConfig.helperText,
          config: {
            allowMultipleContracts: true,
            maxContracts: oldFieldConfig.arrayConfig?.maxItems,
            showMiniCards: true
          }
        },
        dependencies: {
          // Dependencies need to be provided by the application
          providers: [],
          products: [],
          categoryOptions: []
        }
      }

    case 'payment':
    case 'payment-modal':
      return {
        stepConfig: {
          id: oldFieldConfig.name,
          type: 'payment',
          title: oldFieldConfig.label,
          description: oldFieldConfig.helperText,
          config: {
            allowedPaymentTypes: ["creditCard", "sepa"],
            requireBillingAddress: true,
            maxPaymentMethods: oldFieldConfig.arrayConfig?.maxItems
          }
        },
        dependencies: {
          // User profile needs to be provided by the application
          userProfile: null
        }
      }

    default:
      console.warn(`Cannot migrate dynamic-array with componentName: ${componentName}`)
      return null
  }
}
