'use client'

import React from 'react'

import { useTranslations } from 'next-intl'
import { FieldValues, UseFormReturn } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

import {
  ContractsStepConfig,
  FormStepConfig,
  FormStepperCallbacks,
  PaymentStepConfig,
  StepConfig,
  StepDependencies,
} from '../types'
import { FieldRenderer } from './field-renderer'

interface StepRendererProps<TFieldValues extends FieldValues = FieldValues> {
  stepConfig: StepConfig
  form: UseFormReturn<TFieldValues>
  disabled?: boolean
  className?: string
  callbacks?: FormStepperCallbacks
  stepDependencies?: StepDependencies
}

/**
 * StepRenderer component that handles rendering different step types
 *
 * This component acts as an adapter between the dynamic form system and specialized step components.
 * It handles:
 * - Form steps: renders fields using FieldRenderer (existing behavior)
 * - Contracts steps: renders ContractsStep component with required dependencies
 * - Payment steps: renders PaymentStep component with required dependencies
 */
export function StepRenderer<TFieldValues extends FieldValues = FieldValues>({
  stepConfig,
  form,
  disabled = false,
  className,
  callbacks,
  stepDependencies,
}: StepRendererProps<TFieldValues>) {
  // Handle different step types using discriminated union
  switch (stepConfig.type) {
    case 'form':
      return (
        <FormStepRenderer
          stepConfig={stepConfig}
          form={form}
          disabled={disabled}
          className={className}
          callbacks={callbacks}
        />
      )

    case 'contracts':
      return (
        <ContractsStepRenderer
          stepConfig={stepConfig}
          form={form}
          disabled={disabled}
          className={className}
          callbacks={callbacks}
          dependencies={stepDependencies?.contracts}
        />
      )

    case 'payment':
      return (
        <PaymentStepRenderer
          stepConfig={stepConfig}
          form={form}
          disabled={disabled}
          className={className}
          callbacks={callbacks}
          dependencies={stepDependencies?.payment}
        />
      )

    default:
      // TypeScript should prevent this, but handle gracefully
      const unknownType = (stepConfig as { type: string }).type
      console.warn(`Unsupported step type: ${unknownType}`)
      return (
        <Card className={className}>
          <CardContent>
            <p className="text-sm text-muted-foreground">Unsupported step type: {unknownType}</p>
          </CardContent>
        </Card>
      )
  }
}

/**
 * Renders form steps with fields (existing behavior)
 */
function FormStepRenderer<TFieldValues extends FieldValues = FieldValues>({
  stepConfig,
  form,
  disabled,
  className,
  callbacks,
}: {
  stepConfig: FormStepConfig
  form: UseFormReturn<TFieldValues>
  disabled?: boolean
  className?: string
  callbacks?: FormStepperCallbacks
}) {
  const t = useTranslations()

  // Calculate grid class based on columns
  const gridClass = `grid-cols-${stepConfig.columns || 1}`

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{t(stepConfig.title)}</CardTitle>
        {stepConfig.description && <p className="text-sm text-muted-foreground">{t(stepConfig.description)}</p>}
      </CardHeader>
      <CardContent>
        <div className={cn('grid gap-4', gridClass)}>
          {stepConfig.fields.map((field) => (
            <FieldRenderer
              key={field.name}
              field={field}
              form={form}
              sectionId={stepConfig.id}
              disabled={disabled}
              callbacks={callbacks}
              stepId={stepConfig.id}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Renders contracts step with specialized component
 */

function ContractsStepRenderer<TFieldValues extends FieldValues = FieldValues>({
  stepConfig,
  form: _form,
  disabled: _disabled,
  className,
  callbacks: _callbacks,
  dependencies,
}: {
  stepConfig: ContractsStepConfig
  form: UseFormReturn<TFieldValues>
  disabled?: boolean
  className?: string
  callbacks?: FormStepperCallbacks
  dependencies?: StepDependencies['contracts']
}) {
  const t = useTranslations()

  // For now, render a placeholder until we integrate the actual ContractsStep component
  // This will be updated in the next task when we modify DynamicForm
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{t(stepConfig.title)}</CardTitle>
        {stepConfig.description && <p className="text-sm text-muted-foreground">{t(stepConfig.description)}</p>}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">Contracts step component will be integrated here.</p>
          {dependencies && (
            <div className="text-xs text-muted-foreground">
              <p>Available dependencies:</p>
              <ul className="list-disc list-inside ml-4">
                <li>Providers: {dependencies.providers?.length || 0}</li>
                <li>Products: {dependencies.products?.length || 0}</li>
                <li>Category Options: {dependencies.categoryOptions?.length || 0}</li>
              </ul>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Renders payment step with specialized component
 */

function PaymentStepRenderer<TFieldValues extends FieldValues = FieldValues>({
  stepConfig,
  form: _form,
  disabled: _disabled,
  className,
  callbacks: _callbacks,
  dependencies,
}: {
  stepConfig: PaymentStepConfig
  form: UseFormReturn<TFieldValues>
  disabled?: boolean
  className?: string
  callbacks?: FormStepperCallbacks
  dependencies?: StepDependencies['payment']
}) {
  const t = useTranslations()

  // For now, render a placeholder until we integrate the actual PaymentStep component
  // This will be updated in the next task when we modify DynamicForm
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{t(stepConfig.title)}</CardTitle>
        {stepConfig.description && <p className="text-sm text-muted-foreground">{t(stepConfig.description)}</p>}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">Payment step component will be integrated here.</p>
          {dependencies && (
            <div className="text-xs text-muted-foreground">
              <p>Available dependencies:</p>
              <ul className="list-disc list-inside ml-4">
                <li>User Profile: {dependencies.userProfile ? 'Available' : 'Not provided'}</li>
              </ul>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
