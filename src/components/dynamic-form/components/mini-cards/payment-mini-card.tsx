'use client'

import React from 'react'

import { Edit2Icon, Trash2Icon } from 'lucide-react'
import { useTranslations } from 'next-intl'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'

import { CreditCardInputs } from '@/modules/data-management/types/payment-schema'
import { CardTypeIcon } from '@/modules/data-management/components/steps/payment/card-type-icon'
import { getCardTypeDisplayName } from '@/modules/data-management/components/steps/payment/card-detection.utils'

interface PaymentMiniCardProps {
  item: CreditCardInputs
  index: number
  onEdit: () => void
  onDelete: () => void
  disabled?: boolean
  miniCardFields?: string[]
}

export function PaymentMiniCard({
  item,
  index,
  onEdit,
  onDelete,
  disabled = false,
  miniCardFields = ['cardHolderName', 'cardNumber', 'expiryDate'],
}: PaymentMiniCardProps) {
  const t = useTranslations()

  // Format card number to show only last 4 digits
  const formatCardNumber = (cardNumber: string) => {
    const cleanNumber = cardNumber.replace(/\D/g, '')
    if (cleanNumber.length >= 4) {
      return `•••• •••• •••• ${cleanNumber.slice(-4)}`
    }
    return cardNumber
  }

  // Get field value based on field name
  const getFieldValue = (fieldName: string) => {
    switch (fieldName) {
      case 'cardHolderName':
        return item.cardHolderName
      case 'cardNumber':
        return formatCardNumber(item.cardNumber)
      case 'expiryDate':
        return item.expiryDate
      case 'cardType':
        return getCardTypeDisplayName(item.cardType)
      case 'billingAddress':
        return item.billingAddress
          ? `${item.billingAddress.street}, ${item.billingAddress.city}`
          : ''
      default:
        return ''
    }
  }

  // Get field label based on field name
  const getFieldLabel = (fieldName: string) => {
    switch (fieldName) {
      case 'cardHolderName':
        return t('PAYMENT.CREDIT_CARD.CARDHOLDER_NAME.LABEL')
      case 'cardNumber':
        return t('PAYMENT.CREDIT_CARD.CARD_NUMBER.LABEL')
      case 'expiryDate':
        return t('PAYMENT.CREDIT_CARD.EXPIRY_DATE.LABEL')
      case 'cardType':
        return t('PAYMENT.CREDIT_CARD.CARD_TYPE.LABEL')
      case 'billingAddress':
        return t('PAYMENT.CREDIT_CARD.BILLING_ADDRESS.LABEL')
      default:
        return fieldName
    }
  }

  return (
    <Card className="relative group hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CardTypeIcon cardType={item.cardType} className="w-8 h-5" />
            <div>
              <h4 className="font-medium text-sm">
                {item.cardHolderName || t('PAYMENT.CREDIT_CARD.UNNAMED_CARD')}
              </h4>
              <p className="text-xs text-muted-foreground">
                {formatCardNumber(item.cardNumber)}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={onEdit}
              disabled={disabled}
              className="h-8 w-8 p-0"
            >
              <Edit2Icon className="w-4 h-4" />
              <span className="sr-only">{t('COMMON.ACTIONS.EDIT')}</span>
            </Button>

            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={onDelete}
              disabled={disabled}
              className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2Icon className="w-4 h-4" />
              <span className="sr-only">{t('COMMON.ACTIONS.DELETE')}</span>
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-2">
          {miniCardFields.map((fieldName) => {
            const value = getFieldValue(fieldName)
            if (!value || fieldName === 'cardHolderName' || fieldName === 'cardNumber') {
              return null // Skip empty values and already displayed fields
            }

            return (
              <div key={fieldName} className="flex justify-between items-center text-xs">
                <span className="text-muted-foreground">{getFieldLabel(fieldName)}:</span>
                <span className="font-medium">{value}</span>
              </div>
            )
          })}

          {/* Always show expiry date if not in miniCardFields */}
          {!miniCardFields.includes('expiryDate') && item.expiryDate && (
            <div className="flex justify-between items-center text-xs">
              <span className="text-muted-foreground">
                {t('PAYMENT.CREDIT_CARD.EXPIRY_DATE.LABEL')}:
              </span>
              <span className="font-medium">{item.expiryDate}</span>
            </div>
          )}

          {/* Card type indicator */}
          {item.cardType !== 'unknown' && (
            <div className="flex justify-between items-center text-xs">
              <span className="text-muted-foreground">
                {t('PAYMENT.CREDIT_CARD.CARD_TYPE.LABEL')}:
              </span>
              <span className="font-medium">{getCardTypeDisplayName(item.cardType)}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
