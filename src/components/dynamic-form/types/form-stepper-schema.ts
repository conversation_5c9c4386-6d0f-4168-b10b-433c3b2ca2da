import { z } from 'zod'

// Define supported field types based on existing form components
export const FieldTypeEnum = z.enum([
  'input',
  'textarea',
  'select',
  'multiselect',
  'checkbox',
  'radio',
  'switch',
  'date',
  'datetime',
  'month',
  'phone',
  'country',
  'upload',
] as const)

export type FieldType = z.infer<typeof FieldTypeEnum>

// Define validation rule types
export const ValidationRuleTypeEnum = z.enum([
  'required',
  'minLength',
  'maxLength',
  'min',
  'max',
  'email',
  'regex',
  'isTrue',
  'custom',
] as const)

export type ValidationRuleType = z.infer<typeof ValidationRuleTypeEnum>

// Define condition operators for conditional field display
export const ConditionOperatorEnum = z.enum([
  'equals',
  'notEquals',
  'contains',
  'notContains',
  'greaterThan',
  'lessThan',
  'greaterThanOrEqual',
  'lessThanOrEqual',
  'isEmpty',
  'isNotEmpty',
] as const)

export type ConditionOperator = z.infer<typeof ConditionOperatorEnum>

// Define logical operators for complex conditions
export const LogicalOperatorEnum = z.enum(['and', 'or'] as const)
export type LogicalOperator = z.infer<typeof LogicalOperatorEnum>

// Validation rule schema
export const ValidationRuleSchema = z.object({
  type: ValidationRuleTypeEnum,
  value: z.union([z.string(), z.number(), z.boolean()]).optional(),
  message: z.string(),
  pattern: z.string().optional(), // For regex validation
})

export type ValidationRule = z.infer<typeof ValidationRuleSchema>

// Condition schema for conditional field display
export const ConditionSchema = z.object({
  field: z.string(), // Field name to check
  operator: ConditionOperatorEnum,
  value: z.union([z.string(), z.number(), z.boolean(), z.array(z.string())]).optional(),
})

export type Condition = z.infer<typeof ConditionSchema>

// Complex condition schema with logical operators

export const ComplexConditionSchema: z.ZodType<{
  operator: LogicalOperator
  conditions: (Condition | { operator: LogicalOperator; conditions: any[] })[]
}> = z.lazy(() =>
  z.object({
    operator: LogicalOperatorEnum,
    conditions: z.array(z.union([ConditionSchema, ComplexConditionSchema])),
  })
)

export type ComplexCondition = z.infer<typeof ComplexConditionSchema>

// Option schema for select/radio fields
export const OptionSchema = z.object({
  value: z.string(),
  label: z.string(),
  disabled: z.boolean().optional(),
})

export type Option = z.infer<typeof OptionSchema>

// Field configuration schema
export const FieldConfigSchema = z.object({
  name: z.string(),
  type: FieldTypeEnum,
  label: z.string(),
  placeholder: z.string().optional(),
  helperText: z.string().optional(),
  tooltip: z.string().optional(),
  isRequired: z.boolean().optional().default(false),
  disabled: z.boolean().optional().default(false),
  className: z.string().optional(),
  colSpan: z.number().min(1).max(12).optional().default(1), // Grid column span

  // Field-specific properties
  options: z.array(OptionSchema).optional(), // For select/radio/multiselect
  multiple: z.boolean().optional(), // For select (multiselect)
  maxCount: z.number().optional(), // For multiselect (max items to display)
  maxFiles: z.number().optional(), // For upload
  accept: z.string().optional(), // For upload
  maxSize: z.number().optional(), // For upload (in MB)
  includeTime: z.boolean().optional(), // For date fields
  includeSeconds: z.boolean().optional(), // For datetime fields
  dateOnly: z.boolean().optional(), // For date fields
  mode: z.enum(['date', 'datetime', 'month']).optional(), // For calendar fields
  suffix: z.string().optional(), // For input fields (e.g., currency symbol)
  maxLength: z.number().optional(), // For textarea
  showCounter: z.boolean().optional(), // For textarea
  autoComplete: z.boolean().optional().default(true), // For select fields
  searchPlaceholder: z.string().optional(), // For select fields
  emptyMessage: z.string().optional(), // For select fields
  isNationality: z.boolean().optional(), // For country select

  // Validation rules
  validation: z.array(ValidationRuleSchema).optional(),

  // Conditional display
  showIf: z.union([ConditionSchema, ComplexConditionSchema]).optional(),

  // Default value
  defaultValue: z.union([z.string(), z.number(), z.boolean(), z.date(), z.array(z.string())]).optional(),
})

export type FieldConfig = z.infer<typeof FieldConfigSchema>

// Step type enum for discriminated union
export const StepTypeEnum = z.enum(['form', 'contracts', 'payment'] as const)
export type StepType = z.infer<typeof StepTypeEnum>

// Base step configuration
const BaseStepConfigSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().optional(),
})

// Form step configuration (existing behavior)
const FormStepConfigSchema = BaseStepConfigSchema.extend({
  type: z.literal('form'),
  columns: z.number().min(1).max(12).optional().default(1), // Grid columns for this step
  fields: z.array(FieldConfigSchema),
})

// Contracts step configuration
const ContractsStepConfigSchema = BaseStepConfigSchema.extend({
  type: z.literal('contracts'),
  config: z
    .object({
      allowMultipleContracts: z.boolean().optional().default(true),
      maxContracts: z.number().optional(),
      requiredFields: z.array(z.string()).optional(),
      showMiniCards: z.boolean().optional().default(true),
    })
    .optional(),
})

// Payment step configuration
const PaymentStepConfigSchema = BaseStepConfigSchema.extend({
  type: z.literal('payment'),
  config: z
    .object({
      allowedPaymentTypes: z.array(z.enum(['creditCard', 'sepa', 'cash', 'cheque'])).optional(),
      requireBillingAddress: z.boolean().optional().default(true),
      maxPaymentMethods: z.number().optional(),
    })
    .optional(),
})

// Discriminated union for step configuration
export const StepConfigSchema = z.discriminatedUnion('type', [
  FormStepConfigSchema,
  ContractsStepConfigSchema,
  PaymentStepConfigSchema,
])

export type StepConfig = z.infer<typeof StepConfigSchema>
export type FormStepConfig = z.infer<typeof FormStepConfigSchema>
export type ContractsStepConfig = z.infer<typeof ContractsStepConfigSchema>
export type PaymentStepConfig = z.infer<typeof PaymentStepConfigSchema>

// Form configuration schema
export const FormConfigSchema = z.object({
  title: z.string(),
  description: z.string().optional(),
  logoUrl: z.string().optional(),
  submitButtonText: z.string().optional().default('Submit'),
  nextButtonText: z.string().optional().default('Next'),
  previousButtonText: z.string().optional().default('Previous'),
  showStepNumbers: z.boolean().optional().default(true),
  showProgressBar: z.boolean().optional().default(true),
  allowStepNavigation: z.boolean().optional().default(false), // Allow clicking on step indicators
  validateOnStepChange: z.boolean().optional().default(true),
  showStepper: z.boolean().optional(), // Explicit control over stepper visibility
  showNavigationButtons: z.boolean().optional(), // Control navigation buttons visibility
  steps: z.array(StepConfigSchema).min(1),
})

export type FormConfig = z.infer<typeof FormConfigSchema>

// Legacy aliases for backward compatibility
export const FormStepperConfigSchema = FormConfigSchema
export type FormStepperConfig = FormConfig

// Form data type - will be dynamically typed based on field configurations
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type FormData = Record<string, any>

// Step validation result
export interface StepValidationResult {
  isValid: boolean
  errors: Record<string, string[]>
}

// Form stepper state
export interface FormStepperState {
  currentStep: number
  completedSteps: Set<number>
  stepValidation: Record<number, StepValidationResult>
  isSubmitting: boolean
  hasSubmitted: boolean
}

// Step dependencies for specialized steps
export interface StepDependencies {
  contracts?: {
    providers: any[] // Provider[] - using any to avoid circular dependencies
    products: any[] // Product[] - using any to avoid circular dependencies
    categoryOptions: { value: string; label: string }[]
  }
  payment?: {
    userProfile: any // UserProfile - using any to avoid circular dependencies
  }
}

// Form stepper callbacks
export interface FormStepperCallbacks {
  onSubmit: (data: FormData) => Promise<void> | void
  onStepChange?: ({
    currentStepData,
    currentStepId,
    step,
    direction,
  }: {
    currentStepData: Record<string, any>
    currentStepId: string
    step: number
    direction: 'next' | 'previous'
  }) => void
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onFieldChange?: (fieldName: string, value: any) => void
  onValidationChange?: (step: number, validation: StepValidationResult) => void
}
