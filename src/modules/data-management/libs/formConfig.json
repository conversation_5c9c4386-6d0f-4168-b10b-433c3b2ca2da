{"title": "DATA_MANAGEMENT.FORM.TITLE", "description": "DATA_MANAGEMENT.FORM.DESCRIPTION", "submitButtonText": "DATA_MANAGEMENT.FORM.SUBMIT", "nextButtonText": "DATA_MANAGEMENT.FORM.NEXT", "previousButtonText": "DATA_MANAGEMENT.FORM.PREVIOUS", "showStepNumbers": true, "showProgressBar": true, "allowStepNavigation": false, "validateOnStepChange": true, "steps": [{"id": "clientInformation", "type": "form", "title": "DATA_MANAGEMENT.STEPS.CLIENT_INFORMATION.TITLE", "description": "DATA_MANAGEMENT.STEPS.CLIENT_INFORMATION.DESCRIPTION", "columns": 2, "fields": [{"name": "salutation", "type": "select", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.SALUTATION_FIELD.LABEL", "isRequired": false, "colSpan": 1, "options": [{"value": "MISTER", "label": "LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.MISTER"}, {"value": "MISSIS", "label": "LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.MISSIS"}, {"value": "UNKNOWN", "label": "LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.UNKNOWN"}, {"value": "COMPANY", "label": "LOGIN.SIGNUP.SALUTATION_FIELD.OPTION.COMPANY"}]}, {"name": "title", "type": "input", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.TITLE_FIELD.LABEL", "isRequired": false, "colSpan": 1}, {"name": "firstName", "type": "input", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.FIRST_NAME_FIELD.LABEL", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "PROFILE.VALIDATION.FIRST_NAME_REQUIRED"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "PROFILE.VALIDATION.FIRST_NAME_REQUIRED"}]}, {"name": "lastName", "type": "input", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.LAST_NAME_FIELD.LABEL", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "PROFILE.VALIDATION.LAST_NAME_REQUIRED"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "PROFILE.VALIDATION.LAST_NAME_REQUIRED"}]}, {"name": "familyStatus", "type": "select", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.LABEL", "isRequired": false, "colSpan": 1, "options": [{"value": "SINGLE", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.SINGLE"}, {"value": "MARRIED", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.MARRIED"}, {"value": "DIVORCED", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.DIVORCED"}, {"value": "WIDOWED", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.WIDOWED"}, {"value": "SEPARATED", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.SEPARATED"}, {"value": "PARTNERSHIP", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.PARTNERSHIP"}, {"value": "COMMUNITY", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.COMMUNITY"}, {"value": "UNKNOWN", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.MARTIAL_STATUS_FIELD.OPTION.UNKNOWN"}]}, {"name": "<PERSON><PERSON><PERSON>", "type": "input", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.BIRTH_NAME_FIELD.LABEL", "isRequired": false, "colSpan": 1}, {"name": "birthdate", "type": "date", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.BIRTHDATE_FIELD.LABEL", "isRequired": false, "colSpan": 1, "mode": "date"}, {"name": "birthPlace", "type": "input", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.BIRTH_PLACE_FIELD.LABEL", "isRequired": false, "colSpan": 1}, {"name": "birthCountry", "type": "country", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.BIRTH_COUNTRY_FIELD.LABEL", "isRequired": false, "colSpan": 1, "isNationality": false}, {"name": "nationality", "type": "country", "label": "PROFILE.EDIT_FORM.MY_DATA_FIELDSET.NATIONALITY_FIELD.LABEL", "isRequired": false, "colSpan": 1, "isNationality": true}]}, {"id": "legitimation", "type": "form", "title": "DATA_MANAGEMENT.STEPS.LEGITIMATION.TITLE", "description": "DATA_MANAGEMENT.STEPS.LEGITIMATION.DESCRIPTION", "columns": 2, "fields": [{"name": "idCardNumber", "type": "input", "label": "PROFILE.EDIT_FORM.LEGITIMATION_FIELDSET.ID_CARD_NUMBER_FIELD.LABEL", "isRequired": false, "colSpan": 1}, {"name": "idCardDate", "type": "date", "label": "PROFILE.EDIT_FORM.LEGITIMATION_FIELDSET.ID_CARD_DATE_FIELD.LABEL", "isRequired": false, "colSpan": 1, "mode": "date"}, {"name": "idCardAuthority", "type": "input", "label": "PROFILE.EDIT_FORM.LEGITIMATION_FIELDSET.ID_CARD_AUTHORITY_FIELD.LABEL", "isRequired": false, "colSpan": 2}]}, {"id": "defaultAddress", "type": "form", "title": "DATA_MANAGEMENT.STEPS.DEFAULT_ADDRESS.TITLE", "description": "DATA_MANAGEMENT.STEPS.DEFAULT_ADDRESS.DESCRIPTION", "columns": 2, "fields": [{"name": "street", "type": "input", "label": "PROFILE.EDIT_FORM.ADDRESS_FIELDSET.STREET_FIELD.LABEL", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "PROFILE.VALIDATION.STREET_REQUIRED"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "PROFILE.VALIDATION.STREET_REQUIRED"}]}, {"name": "streetNum", "type": "input", "label": "PROFILE.EDIT_FORM.ADDRESS_FIELDSET.STREET_NUM_FIELD.LABEL", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "PROFILE.VALIDATION.STREET_NUM_REQUIRED"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "PROFILE.VALIDATION.STREET_NUM_REQUIRED"}]}, {"name": "zip", "type": "input", "label": "PROFILE.EDIT_FORM.ADDRESS_FIELDSET.ZIP_FIELD.LABEL", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "PROFILE.VALIDATION.ZIP_REQUIRED"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "PROFILE.VALIDATION.ZIP_REQUIRED"}]}, {"name": "city", "type": "input", "label": "PROFILE.EDIT_FORM.ADDRESS_FIELDSET.CITY_FIELD.LABEL", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "PROFILE.VALIDATION.CITY_REQUIRED"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "PROFILE.VALIDATION.CITY_REQUIRED"}]}, {"name": "country", "type": "country", "label": "PROFILE.EDIT_FORM.ADDRESS_FIELDSET.COUNTRY_FIELD.LABEL", "isRequired": true, "colSpan": 1, "isNationality": false, "validation": [{"type": "required", "message": "PROFILE.VALIDATION.COUNTRY_REQUIRED"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "PROFILE.VALIDATION.COUNTRY_REQUIRED"}]}, {"name": "district", "type": "input", "label": "PROFILE.EDIT_FORM.ADDRESS_FIELDSET.DISTRICT_FIELD.LABEL", "isRequired": false, "colSpan": 1}]}, {"id": "contactData", "type": "form", "title": "DATA_MANAGEMENT.STEPS.CONTACT_DATA.TITLE", "description": "DATA_MANAGEMENT.STEPS.CONTACT_DATA.DESCRIPTION", "columns": 2, "fields": [{"name": "email", "type": "input", "label": "PROFILE.EDIT_FORM.CONTACT_FIELDSET.EMAIL_FIELD.LABEL", "isRequired": false, "colSpan": 2, "validation": [{"type": "email", "message": "not Valid"}]}, {"name": "preferredContactType", "type": "select", "label": "PROFILE.EDIT_FORM.CONTACT_FIELDSET.PREFERRED_CONTACT_TYPE_FIELD.LABEL", "isRequired": false, "colSpan": 1, "options": [{"value": "email", "label": "CONTACT.TYPE.EMAIL"}, {"value": "phone", "label": "CONTACT.TYPE.PHONE"}, {"value": "sms", "label": "CONTACT.TYPE.SMS"}]}, {"name": "preferredAppeal", "type": "select", "label": "PROFILE.EDIT_FORM.CONTACT_FIELDSET.PREFERRED_APPEAL_FIELD.LABEL", "isRequired": false, "colSpan": 1, "options": [{"value": "formal", "label": "CONTACT.APPEAL.FORMAL"}, {"value": "informal", "label": "CONTACT.APPEAL.INFORMAL"}]}, {"name": "phone", "type": "phone", "label": "PROFILE.EDIT_FORM.CONTACT_FIELDSET.PHONE_FIELD.LABEL", "isRequired": false, "colSpan": 1}, {"name": "mobile", "type": "phone", "label": "PROFILE.EDIT_FORM.CONTACT_FIELDSET.MOBILE_FIELD.LABEL", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "not Valid"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "not Valid"}]}, {"name": "businessPhone", "type": "phone", "label": "PROFILE.EDIT_FORM.CONTACT_FIELDSET.BUSINESS_PHONE_FIELD.LABEL", "isRequired": false, "colSpan": 1}]}, {"id": "profession", "type": "form", "title": "DATA_MANAGEMENT.STEPS.PROFESSION.TITLE", "description": "DATA_MANAGEMENT.STEPS.PROFESSION.DESCRIPTION", "columns": 2, "fields": [{"name": "profession", "type": "input", "label": "PROFESSION.EDIT.PROFESSION_FIELD.LABEL", "isRequired": true, "colSpan": 2, "validation": [{"type": "required", "message": "this field is required"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "this field is required"}]}, {"name": "jobType", "type": "select", "label": "PROFESSION.EDIT.JOB_TYPE_FIELD.LABEL", "isRequired": false, "colSpan": 1, "options": [{"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"value": "Se<PERSON><PERSON>tändi<PERSON>", "label": "Se<PERSON><PERSON>tändi<PERSON>"}, {"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"value": "Be<PERSON><PERSON> auf Lebenszeit", "label": "Be<PERSON><PERSON> auf Lebenszeit"}, {"value": "Beamter im höheren Dienst", "label": "Beamter im höheren Dienst"}, {"value": "<PERSON><PERSON><PERSON> auf <PERSON>", "label": "<PERSON><PERSON><PERSON> auf <PERSON>"}, {"value": "<PERSON><PERSON><PERSON> auf <PERSON>", "label": "<PERSON><PERSON><PERSON> auf <PERSON>"}, {"value": "Arbeiter öffentl. Dienst", "label": "Arbeiter öffentl. Dienst"}, {"value": "Angestellter öffentl. Dienst", "label": "Angestellter öffentl. Dienst"}, {"value": "Geschäftsf. Gesellschafter", "label": "Geschäftsf. Gesellschafter"}, {"value": "Vorstand", "label": "Vorstand"}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Student", "label": "Student"}, {"value": "Wehr-.Zivildienst.FSJ", "label": "Wehr-.Zivildienst.FSJ"}, {"value": "Hausfrau<PERSON>", "label": "Hausfrau<PERSON>"}, {"value": "In Elternzeit", "label": "In Elternzeit"}, {"value": "Rentner.Ruheständler", "label": "Rentner.Ruheständler"}, {"value": "Arbeitssuchend", "label": "Arbeitssuchend"}]}, {"name": "workPercentagePhysical", "type": "input", "label": "PROFESSION.EDIT.WORK_PERCENTAGE_PHYSICAL_FIELD.LABEL", "isRequired": false, "colSpan": 1, "suffix": "%", "validation": [{"type": "min", "value": 0, "message": "PROFESSION.VALIDATION.WORK_PERCENTAGE_MIN"}, {"type": "max", "value": 100, "message": "PROFESSION.VALIDATION.WORK_PERCENTAGE_MAX"}]}, {"name": "workPercentageOffice", "type": "input", "label": "PROFESSION.EDIT.WORK_PERCENTAGE_OFFICE_FIELD.LABEL", "isRequired": false, "colSpan": 1, "suffix": "%", "validation": [{"type": "min", "value": 0, "message": "PROFESSION.VALIDATION.WORK_PERCENTAGE_MIN"}, {"type": "max", "value": 100, "message": "PROFESSION.VALIDATION.WORK_PERCENTAGE_MAX"}]}, {"name": "income<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "input", "label": "PROFESSION.EDIT.INCOME_YEARLY_BRUTTO_FIELD.LABEL", "isRequired": true, "colSpan": 1, "suffix": "€", "validation": [{"type": "required", "message": "this field is required"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "this field is required"}]}, {"name": "incomeYearly<PERSON>to", "type": "input", "label": "PROFESSION.EDIT.INCOME_YEARLY_NETTO_FIELD.LABEL", "isRequired": true, "colSpan": 1, "suffix": "€", "validation": [{"type": "required", "message": "this field is required"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "this field is required"}]}, {"name": "incomeYearlySalaries", "type": "input", "label": "PROFESSION.EDIT.INCOME_YEARLY_SALARIES_FIELD.LABEL", "isRequired": true, "colSpan": 1, "suffix": "€", "validation": [{"type": "required", "message": "this field is required"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "this field is required"}]}, {"name": "retirementSavingSince", "type": "date", "label": "PROFESSION.EDIT.RETIREMENT_SAVING_SINCE_FIELD.LABEL", "isRequired": false, "colSpan": 1, "mode": "date"}, {"name": "capitalFormingPayments", "type": "input", "label": "PROFESSION.EDIT.CAPITAL_FORMING_PAYMENTS_FIELD.LABEL", "isRequired": true, "colSpan": 1, "suffix": "€", "validation": [{"type": "required", "message": "this field is required"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "this field is required"}]}, {"name": "taxOfficeName", "type": "input", "label": "PROFESSION.EDIT.TAX_OFFICE_NAME_FIELD.LABEL", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "this field is required"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "this field is required"}]}, {"name": "taxNumber", "type": "input", "label": "PROFESSION.EDIT.TAX_NUMBER_FIELD.LABEL", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "this field is required"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "this field is required"}]}, {"name": "taxId", "type": "input", "label": "PROFESSION.EDIT.TAX_ID_FIELD.LABEL", "isRequired": true, "colSpan": 1, "validation": [{"type": "required", "message": "this field is required"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 1, "message": "this field is required"}]}, {"name": "taxClass", "type": "select", "label": "PROFESSION.EDIT.TAX_CLASS_FIELD.LABEL", "isRequired": false, "colSpan": 1, "options": [{"value": "_", "label": "<PERSON><PERSON>"}, {"value": "I", "label": "I"}, {"value": "II", "label": "II"}, {"value": "III", "label": "III"}, {"value": "IV", "label": "IV"}, {"value": "V", "label": "V"}, {"value": "VI", "label": "VI"}]}, {"name": "churchTaxPercentage", "type": "select", "label": "PROFESSION.EDIT.CHURCH_TAX_FIELD.LABEL", "isRequired": false, "colSpan": 1, "options": [{"value": "0", "label": "0%"}, {"value": "4", "label": "4%"}, {"value": "5", "label": "5%"}, {"value": "6", "label": "6%"}, {"value": "7", "label": "7%"}, {"value": "8", "label": "8%"}, {"value": "9", "label": "9%"}]}, {"name": "healthInsurance", "type": "input", "label": "PROFESSION.EDIT.HEALTH_INSURANCE_FIELD.LABEL", "isRequired": false, "colSpan": 1}, {"name": "socialInsuranceNumber", "type": "input", "label": "PROFESSION.EDIT.SOCIAL_INSURANCE_NUMBER_FIELD.LABEL", "isRequired": false, "colSpan": 1}]}, {"id": "healthAndRisks", "type": "form", "title": "DATA_MANAGEMENT.STEPS.HEALTH_AND_RISKS.TITLE", "description": "DATA_MANAGEMENT.STEPS.HEALTH_AND_RISKS.DESCRIPTION", "columns": 2, "fields": [{"name": "weight", "type": "input", "label": "RISK_FORM.WEIGHT_FIELD.LABEL", "isRequired": false, "colSpan": 1, "suffix": "kg"}, {"name": "height", "type": "input", "label": "RISK_FORM.HEIGHT_FIELD.LABEL", "isRequired": false, "colSpan": 1, "suffix": "cm"}, {"name": "smoker", "type": "select", "label": "RISK_FORM.SMOKER_FIELD.LABEL", "isRequired": false, "colSpan": 1, "options": [{"value": "NO", "label": "GLOBAL.NO"}, {"value": "YES", "label": "GLOBAL.YES"}, {"value": "UNKNOWN", "label": "GLOBAL.UNKNOWN"}, {"value": "SOMETIMES", "label": "RISK_FORM.SMOKER_FIELD.OPTION.SOMETIMES"}]}, {"name": "healthInfo", "type": "textarea", "label": "RISK_FORM.HEALTH_INFO_FIELD.LABEL", "isRequired": false, "colSpan": 2, "maxLength": 255, "showCounter": true, "validation": [{"type": "max<PERSON><PERSON><PERSON>", "value": 255, "message": "RISK.VALIDATION.HEALTH_INFO_MAX_LENGTH"}]}]}, {"id": "contracts", "type": "contracts", "title": "DATA_MANAGEMENT.STEPS.CONTRACTS.TITLE", "description": "DATA_MANAGEMENT.STEPS.CONTRACTS.DESCRIPTION", "config": {"allowMultipleContracts": true, "maxContracts": 10, "showMiniCards": true, "requiredFields": ["insurance_number", "payment", "payment_period", "start", "end"]}}, {"id": "payment", "type": "payment", "title": "DATA_MANAGEMENT.STEPS.PAYMENT.TITLE", "description": "DATA_MANAGEMENT.STEPS.PAYMENT.DESCRIPTION", "config": {"allowedPaymentTypes": ["creditCard"], "requireBillingAddress": true, "maxPaymentMethods": 5}}]}