'use client'

import { Card, CardContent } from '@/components/ui/card'
import { DynamicForm, FormConfig, FormStepperCallbacks } from '@/components/dynamic-form'

import dataManagementConfig from '../libs/formConfig.json'

export function DataManagementDynamicForm() {
  const multiStepCallbacks: FormStepperCallbacks = {
    onSubmit: async (data) => {
      console.log('Form submitted:', data)
      // Handle final form submission
    },
    onStepChange: async ({ currentStepData, currentStepId, step, direction }) => {
      console.log('Step changed:', currentStepData, currentStepId, step, direction)
      return true
    },
  }
  return (
    <Card className="w-full">
      <CardContent>
        <DynamicForm config={dataManagementConfig as FormConfig} callbacks={multiStepCallbacks} />
      </CardContent>
    </Card>
  )
}
